@extends('documents.base')

@section('title', 'Analytics Report - ' . $documentNumber)
@section('document-title', 'ANALYTICS REPORT')

@section('header-right-extra')
    <div style="margin-top: 10px;">
        <span class="badge badge-info">{{ $reportPeriod ?? 'Custom Period' }}</span>
    </div>
@endsection

@section('content')
    <!-- Report Summary -->
    <div class="info-box">
        <div class="info-box-title">Report Summary</div>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; padding: 5px 0; width: 25%;"><strong>Report Type:</strong></td>
                <td style="border: none; padding: 5px 0; width: 25%;">{{ $reportType ?? 'General Analytics' }}</td>
                <td style="border: none; padding: 5px 0; width: 25%;"><strong>Period:</strong></td>
                <td style="border: none; padding: 5px 0; width: 25%;">{{ $reportPeriod ?? 'Custom' }}</td>
            </tr>
            <tr>
                <td style="border: none; padding: 5px 0;"><strong>Date Range:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ $startDate ?? 'N/A' }} - {{ $endDate ?? 'N/A' }}</td>
                <td style="border: none; padding: 5px 0;"><strong>Generated By:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ $generatedBy ?? 'System' }}</td>
            </tr>
        </table>
    </div>

    <!-- Key Metrics -->
    @if(isset($keyMetrics))
    <div class="section">
        <div class="section-title">Key Performance Metrics</div>
        <div style="display: table; width: 100%;">
            @foreach($keyMetrics as $metric)
            <div style="display: table-cell; width: {{ 100 / count($keyMetrics) }}%; padding: 0 10px;">
                <div class="info-box" style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 5px;">
                        {{ $metric['value'] ?? 'N/A' }}
                    </div>
                    <div style="font-size: 11px; font-weight: bold; color: #666;">
                        {{ $metric['label'] ?? 'Metric' }}
                    </div>
                    @if(isset($metric['change']))
                    <div style="font-size: 9px; color: {{ $metric['change'] >= 0 ? '#16a34a' : '#dc2626' }}; margin-top: 3px;">
                        {{ $metric['change'] >= 0 ? '+' : '' }}{{ $metric['change'] }}%
                    </div>
                    @endif
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Revenue Analysis -->
    @if(isset($revenueData))
    <div class="section">
        <div class="section-title">Revenue Analysis</div>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 30%;">Period</th>
                    <th style="width: 20%; text-align: right;">Revenue</th>
                    <th style="width: 15%; text-align: right;">Orders</th>
                    <th style="width: 20%; text-align: right;">Avg Order Value</th>
                    <th style="width: 15%; text-align: right;">Growth</th>
                </tr>
            </thead>
            <tbody>
                @foreach($revenueData as $period)
                <tr>
                    <td>{{ $period['period'] ?? 'N/A' }}</td>
                    <td class="text-right">@currency($period['revenue'] ?? 0)</td>
                    <td class="text-right">{{ number_format($period['orders'] ?? 0) }}</td>
                    <td class="text-right">@currency($period['avg_order_value'] ?? 0)</td>
                    <td class="text-right" style="color: {{ ($period['growth'] ?? 0) >= 0 ? '#16a34a' : '#dc2626' }};">
                        {{ ($period['growth'] ?? 0) >= 0 ? '+' : '' }}{{ number_format($period['growth'] ?? 0, 1) }}%
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Customer Analytics -->
    @if(isset($customerData))
    <div class="section">
        <div class="section-title">Customer Analytics</div>
        <div style="display: table; width: 100%;">
            <div style="display: table-cell; width: 50%; padding-right: 10px;">
                <div class="info-box">
                    <div class="info-box-title">Customer Metrics</div>
                    <table style="width: 100%; border: none;">
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Total Customers:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">{{ number_format($customerData['total'] ?? 0) }}</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>New Customers:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">{{ number_format($customerData['new'] ?? 0) }}</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Returning Customers:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">{{ number_format($customerData['returning'] ?? 0) }}</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Customer Retention:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">{{ number_format($customerData['retention_rate'] ?? 0, 1) }}%</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div style="display: table-cell; width: 50%; padding-left: 10px;">
                <div class="info-box">
                    <div class="info-box-title">Customer Value</div>
                    <table style="width: 100%; border: none;">
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Avg Customer Value:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">@currency($customerData['avg_value'] ?? 0)</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Lifetime Value:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">@currency($customerData['lifetime_value'] ?? 0)</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Avg Orders per Customer:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">{{ number_format($customerData['avg_orders'] ?? 0, 1) }}</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Churn Rate:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">{{ number_format($customerData['churn_rate'] ?? 0, 1) }}%</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Service Performance -->
    @if(isset($serviceData))
    <div class="section">
        <div class="section-title">Service Performance</div>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 25%;">Service Type</th>
                    <th style="width: 15%; text-align: right;">Volume</th>
                    <th style="width: 20%; text-align: right;">Revenue</th>
                    <th style="width: 15%; text-align: right;">Avg Price</th>
                    <th style="width: 15%; text-align: right;">On-Time %</th>
                    <th style="width: 10%; text-align: right;">Growth</th>
                </tr>
            </thead>
            <tbody>
                @foreach($serviceData as $service)
                <tr>
                    <td>{{ $service['type'] ?? 'N/A' }}</td>
                    <td class="text-right">{{ number_format($service['volume'] ?? 0) }}</td>
                    <td class="text-right">@currency($service['revenue'] ?? 0)</td>
                    <td class="text-right">@currency($service['avg_price'] ?? 0)</td>
                    <td class="text-right">{{ number_format($service['on_time_rate'] ?? 0, 1) }}%</td>
                    <td class="text-right" style="color: {{ ($service['growth'] ?? 0) >= 0 ? '#16a34a' : '#dc2626' }};">
                        {{ ($service['growth'] ?? 0) >= 0 ? '+' : '' }}{{ number_format($service['growth'] ?? 0, 1) }}%
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Geographic Analysis -->
    @if(isset($geographicData))
    <div class="section">
        <div class="section-title">Geographic Performance</div>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 30%;">Region/Country</th>
                    <th style="width: 20%; text-align: right;">Shipments</th>
                    <th style="width: 25%; text-align: right;">Revenue</th>
                    <th style="width: 25%; text-align: right;">Market Share</th>
                </tr>
            </thead>
            <tbody>
                @foreach($geographicData as $region)
                <tr>
                    <td>{{ $region['name'] ?? 'N/A' }}</td>
                    <td class="text-right">{{ number_format($region['shipments'] ?? 0) }}</td>
                    <td class="text-right">@currency($region['revenue'] ?? 0)</td>
                    <td class="text-right">{{ number_format($region['market_share'] ?? 0, 1) }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Operational Metrics -->
    @if(isset($operationalData))
    <div class="section">
        <div class="section-title">Operational Metrics</div>
        <div style="display: table; width: 100%;">
            <div style="display: table-cell; width: 50%; padding-right: 10px;">
                <div class="info-box">
                    <div class="info-box-title">Delivery Performance</div>
                    <table style="width: 100%; border: none;">
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>On-Time Delivery:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">{{ number_format($operationalData['on_time_rate'] ?? 0, 1) }}%</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Avg Delivery Time:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">{{ $operationalData['avg_delivery_time'] ?? 'N/A' }} days</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Damage Rate:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">{{ number_format($operationalData['damage_rate'] ?? 0, 2) }}%</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Customer Satisfaction:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">{{ number_format($operationalData['satisfaction_score'] ?? 0, 1) }}/5.0</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div style="display: table-cell; width: 50%; padding-left: 10px;">
                <div class="info-box">
                    <div class="info-box-title">Efficiency Metrics</div>
                    <table style="width: 100%; border: none;">
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Cost per Shipment:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">@currency($operationalData['cost_per_shipment'] ?? 0)</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Revenue per Shipment:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">@currency($operationalData['revenue_per_shipment'] ?? 0)</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Profit Margin:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">{{ number_format($operationalData['profit_margin'] ?? 0, 1) }}%</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Capacity Utilization:</strong></td>
                            <td style="border: none; padding: 5px 0; text-align: right;">{{ number_format($operationalData['capacity_utilization'] ?? 0, 1) }}%</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Trends and Insights -->
    @if(isset($insights))
    <div class="section">
        <div class="section-title">Key Insights & Recommendations</div>
        <div class="info-box">
            @foreach($insights as $insight)
            <div style="margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #e9ecef;">
                <div style="font-weight: bold; color: #2563eb; margin-bottom: 5px;">{{ $insight['title'] ?? 'Insight' }}</div>
                <div style="font-size: 11px; line-height: 1.4;">{{ $insight['description'] ?? 'No description available.' }}</div>
                @if(isset($insight['recommendation']))
                <div style="font-size: 10px; color: #16a34a; margin-top: 5px;">
                    <strong>Recommendation:</strong> {{ $insight['recommendation'] }}
                </div>
                @endif
            </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Data Sources and Methodology -->
    <div class="section">
        <div class="section-title">Report Methodology</div>
        <div style="font-size: 10px; line-height: 1.4; color: #666;">
            <p><strong>Data Sources:</strong> This report is compiled from multiple data sources including order management system, customer database, tracking systems, and financial records.</p>
            <p><strong>Calculation Methods:</strong> All metrics are calculated using standard industry formulas. Revenue figures include all applicable taxes and fees.</p>
            <p><strong>Data Quality:</strong> Data accuracy is verified through automated validation processes. Any anomalies are flagged and investigated.</p>
            <p><strong>Reporting Period:</strong> All data is based on the specified reporting period. Comparative data uses the same period from the previous cycle.</p>
            <p><strong>Currency:</strong> All monetary values are displayed in {{ $siteSettings['default_currency'] ?? 'USD' }} unless otherwise specified.</p>
        </div>
    </div>
@endsection

@section('footer')
    <div style="text-align: center; margin-bottom: 10px;">
        <strong>Confidential Business Analytics Report</strong>
    </div>
    <div style="text-align: center; font-size: 10px;">
        Report ID: {{ $documentNumber }} | 
        Generated: {{ $generatedAt->format('M j, Y \a\t g:i A') }} | 
        {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }} Analytics Department
    </div>
@endsection
