@extends('documents.base')

@section('title', 'Delivery Receipt - ' . $parcel->tracking_number)
@section('document-title', 'DELIVERY RECEIPT')

@section('header-right-extra')
    <div style="margin-top: 10px;">
        <span class="badge badge-success">Delivered</span>
    </div>
@endsection

@section('content')
    <!-- Delivery Information -->
    <div class="info-box">
        <div class="info-box-title">Delivery Confirmation</div>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; padding: 5px 0; width: 25%;"><strong>Tracking Number:</strong></td>
                <td style="border: none; padding: 5px 0; width: 25%;">{{ $parcel->tracking_number }}</td>
                <td style="border: none; padding: 5px 0; width: 25%;"><strong>Delivery Date:</strong></td>
                <td style="border: none; padding: 5px 0; width: 25%;">
                    {{ $parcel->delivered_at ? $parcel->delivered_at->format('M j, Y g:i A') : now()->format('M j, Y g:i A') }}
                </td>
            </tr>
            <tr>
                <td style="border: none; padding: 5px 0;"><strong>Service Type:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ ucfirst($parcel->service_type) }}</td>
                <td style="border: none; padding: 5px 0;"><strong>Delivery Status:</strong></td>
                <td style="border: none; padding: 5px 0;">
                    <span class="badge badge-success">Successfully Delivered</span>
                </td>
            </tr>
        </table>
    </div>

    <!-- Sender and Receiver Information -->
    <div class="address-section">
        <div class="address-box">
            <div class="address-title">Sender Information:</div>
            <div class="address-content">
                <strong>{{ $parcel->sender_name }}</strong><br>
                @if($parcel->sender_company)
                    {{ $parcel->sender_company }}<br>
                @endif
                {{ $parcel->sender_address }}<br>
                {{ $parcel->sender_city }}, {{ $parcel->sender_state }} {{ $parcel->sender_postal_code }}<br>
                {{ $parcel->sender_country }}<br>
                @if($parcel->sender_phone)
                    Phone: {{ $parcel->sender_phone }}
                @endif
            </div>
        </div>
        <div class="address-box">
            <div class="address-title">Delivered To:</div>
            <div class="address-content">
                <strong>{{ $parcel->receiver_name }}</strong><br>
                @if($parcel->receiver_company)
                    {{ $parcel->receiver_company }}<br>
                @endif
                {{ $parcel->receiver_address }}<br>
                {{ $parcel->receiver_city }}, {{ $parcel->receiver_state }} {{ $parcel->receiver_postal_code }}<br>
                {{ $parcel->receiver_country }}<br>
                @if($parcel->receiver_phone)
                    Phone: {{ $parcel->receiver_phone }}
                @endif
            </div>
        </div>
    </div>

    <!-- Package Details -->
    <div class="section">
        <div class="section-title">Package Details</div>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 30%;">Description</th>
                    <th style="width: 15%; text-align: center;">Quantity</th>
                    <th style="width: 15%; text-align: center;">Weight</th>
                    <th style="width: 20%; text-align: center;">Dimensions</th>
                    <th style="width: 20%; text-align: right;">Declared Value</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ $parcel->description ?: 'General Package' }}</td>
                    <td class="text-center">{{ $parcel->quantity ?: 1 }}</td>
                    <td class="text-center">{{ $parcel->weight ? $parcel->weight . ' kg' : 'N/A' }}</td>
                    <td class="text-center">
                        @if($parcel->length && $parcel->width && $parcel->height)
                            {{ $parcel->length }} x {{ $parcel->width }} x {{ $parcel->height }} cm
                        @else
                            N/A
                        @endif
                    </td>
                    <td class="text-right">
                        @if($parcel->declared_value)
                            @currency($parcel->declared_value)
                        @else
                            N/A
                        @endif
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Delivery Timeline -->
    @if($parcel->trackingEvents && $parcel->trackingEvents->count() > 0)
    <div class="section">
        <div class="section-title">Delivery Timeline</div>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 20%;">Date & Time</th>
                    <th style="width: 20%;">Status</th>
                    <th style="width: 30%;">Location</th>
                    <th style="width: 30%;">Description</th>
                </tr>
            </thead>
            <tbody>
                @foreach($parcel->trackingEvents->sortBy('created_at') as $event)
                <tr>
                    <td>{{ $event->created_at->format('M j, Y g:i A') }}</td>
                    <td>
                        <span class="badge badge-{{ $event->status === 'delivered' ? 'success' : ($event->status === 'in_transit' ? 'info' : 'warning') }}">
                            {{ ucfirst(str_replace('_', ' ', $event->status)) }}
                        </span>
                    </td>
                    <td>{{ $event->location ?: 'N/A' }}</td>
                    <td>{{ $event->description ?: 'Status updated' }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Delivery Confirmation Details -->
    <div class="section">
        <div class="section-title">Delivery Confirmation</div>
        <div style="display: table; width: 100%;">
            <div style="display: table-cell; width: 50%; padding-right: 10px;">
                <div class="info-box">
                    <div class="info-box-title">Delivery Method</div>
                    <table style="width: 100%; border: none;">
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Delivered To:</strong></td>
                            <td style="border: none; padding: 5px 0;">Recipient</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Location:</strong></td>
                            <td style="border: none; padding: 5px 0;">Front Door</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Signature:</strong></td>
                            <td style="border: none; padding: 5px 0;">Required</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>ID Verified:</strong></td>
                            <td style="border: none; padding: 5px 0;">Yes</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div style="display: table-cell; width: 50%; padding-left: 10px;">
                <div class="info-box">
                    <div class="info-box-title">Package Condition</div>
                    <table style="width: 100%; border: none;">
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Condition:</strong></td>
                            <td style="border: none; padding: 5px 0;">Good</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Damage:</strong></td>
                            <td style="border: none; padding: 5px 0;">None Reported</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Temperature:</strong></td>
                            <td style="border: none; padding: 5px 0;">Normal</td>
                        </tr>
                        <tr>
                            <td style="border: none; padding: 5px 0;"><strong>Seal Intact:</strong></td>
                            <td style="border: none; padding: 5px 0;">Yes</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Service Summary -->
    @if($parcel->shipping_cost || $parcel->insurance_amount || $parcel->cod_amount)
    <div class="section">
        <div class="section-title">Service Summary</div>
        <div class="totals-section">
            <table class="totals-table">
                @if($parcel->shipping_cost)
                <tr>
                    <td class="total-label">Shipping Cost:</td>
                    <td class="total-value">@currency($parcel->shipping_cost)</td>
                </tr>
                @endif
                @if($parcel->insurance_amount)
                <tr>
                    <td class="total-label">Insurance:</td>
                    <td class="total-value">@currency($parcel->insurance_amount)</td>
                </tr>
                @endif
                @if($parcel->additional_charges)
                <tr>
                    <td class="total-label">Additional Charges:</td>
                    <td class="total-value">@currency($parcel->additional_charges)</td>
                </tr>
                @endif
                @if($parcel->cod_amount)
                <tr>
                    <td class="total-label">COD Collected:</td>
                    <td class="total-value">@currency($parcel->cod_amount)</td>
                </tr>
                @endif
                <tr class="grand-total">
                    <td class="total-label">Total Service Value:</td>
                    <td class="total-value">
                        @currency(($parcel->shipping_cost ?? 0) + ($parcel->insurance_amount ?? 0) + ($parcel->additional_charges ?? 0))
                    </td>
                </tr>
            </table>
        </div>
    </div>
    @endif

    <!-- Signatures -->
    <div class="section">
        <div class="section-title">Delivery Confirmation Signatures</div>
        <table style="width: 100%; border: 1px solid #dee2e6;">
            <tr>
                <td style="border: 1px solid #dee2e6; padding: 40px 15px; width: 50%; vertical-align: bottom;">
                    <div style="border-top: 1px solid #333; margin-top: 30px; padding-top: 5px; text-align: center; font-size: 10px;">
                        <strong>Delivery Driver Signature</strong><br>
                        Name: ___________________________<br>
                        Date: {{ now()->format('M j, Y') }} Time: {{ now()->format('g:i A') }}
                    </div>
                </td>
                <td style="border: 1px solid #dee2e6; padding: 40px 15px; width: 50%; vertical-align: bottom;">
                    <div style="border-top: 1px solid #333; margin-top: 30px; padding-top: 5px; text-align: center; font-size: 10px;">
                        <strong>Recipient Signature</strong><br>
                        Name: {{ $parcel->receiver_name }}<br>
                        Date: {{ now()->format('M j, Y') }} Time: {{ now()->format('g:i A') }}
                    </div>
                </td>
            </tr>
        </table>
    </div>

    <!-- Customer Satisfaction -->
    <div class="section">
        <div class="section-title">Customer Satisfaction</div>
        <div class="info-box">
            <p style="margin: 0 0 10px 0; font-size: 11px;"><strong>How was your delivery experience?</strong></p>
            <div style="text-align: center; margin: 15px 0;">
                <span style="font-size: 20px; margin: 0 10px;">😞</span>
                <span style="font-size: 20px; margin: 0 10px;">😐</span>
                <span style="font-size: 20px; margin: 0 10px;">😊</span>
                <span style="font-size: 20px; margin: 0 10px;">😍</span>
            </div>
            <p style="margin: 10px 0 0 0; font-size: 10px; text-align: center;">
                Rate your experience online at {{ $siteSettings['company_website'] ?? 'www.atrixlogistics.com' }}/feedback
            </p>
        </div>
    </div>

    <!-- Important Notice -->
    <div class="section">
        <div class="section-title">Important Notice</div>
        <div style="font-size: 10px; line-height: 1.4; color: #666;">
            <p><strong>Delivery Confirmation:</strong> This receipt confirms successful delivery of your package. Please retain for your records.</p>
            <p><strong>Claims:</strong> Any damage or discrepancy claims must be reported within 24 hours of delivery.</p>
            <p><strong>Feedback:</strong> We value your feedback. Please rate your delivery experience online or contact customer service.</p>
            <p><strong>Future Deliveries:</strong> For delivery preferences or special instructions, please update your account online.</p>
        </div>
    </div>
@endsection

@section('footer')
    <div style="text-align: center; margin-bottom: 10px;">
        <strong>Thank you for choosing {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}!</strong>
    </div>
    <div style="text-align: center; font-size: 10px;">
        Delivery confirmed for tracking #{{ $parcel->tracking_number }} | 
        Customer Service: {{ $siteSettings['company_phone'] ?? 'Phone Number' }} | 
        {{ $siteSettings['company_email'] ?? '<EMAIL>' }}
    </div>
@endsection
